const { app, BrowserWindow, protocol, session } = require('electron');
const path = require('path');
const fs = require('fs');

// 确保用户数据目录存在
const userDataPath = app.getPath('userData');
if (!fs.existsSync(userDataPath)) {
    fs.mkdirSync(userDataPath, { recursive: true });
}

// 设置应用程序参数
app.commandLine.appendSwitch('disable-http-cache');
app.commandLine.appendSwitch('disable-gpu-program-cache');
app.commandLine.appendSwitch('disable-gpu-shader-disk-cache');

async function clearCache() {
    try {
        await app.defaultSession.clearStorageData({
            storages: [
                'appcache',
                'filesystem',
                'indexdb',
                'localstorage',
                'shadercache',
                'websql',
                'serviceworkers',
                'cachestorage'
            ]
        });
        console.log('缓存清理成功');
    } catch (err) {
        console.error('清理缓存时出错:', err);
    }
}

function createWindow() {
    // 创建浏览器窗口
    const win = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: true,
            // 禁用页面级缓存
            partition: 'persist:no-cache',
            // 允许加载本地资源
            webviewTag: true,
            // 启用开发者工具
            devTools: true
        }
    });

    // 设置会话缓存控制
    win.webContents.session.webRequest.onBeforeRequest((details, callback) => {
        callback({});
    });

    // 禁用HTTP缓存
    win.webContents.session.webRequest.onHeadersReceived((details, callback) => {
        callback({
            responseHeaders: {
                ...details.responseHeaders,
                'Cache-Control': ['no-store, no-cache, must-revalidate, proxy-revalidate'],
                'Pragma': ['no-cache'],
                'Expires': ['0'],
                'Surrogate-Control': ['no-store']
            }
        });
    });

    // 加载 index.html
    win.loadFile('index.html');

    // 打开开发者工具（开发时使用）
    if (process.argv.includes('--dev')) {
        win.webContents.openDevTools();
    }

    // 监听加载完成事件
    win.webContents.on('did-finish-load', () => {
        console.log('页面加载完成');
    });

    // 监听加载错误
    win.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
        console.error('页面加载失败:', errorDescription);
    });
}

// 应用准备就绪时创建窗口
app.whenReady().then(async () => {
    // 清理缓存
    await clearCache();
    
    createWindow();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

// 关闭所有窗口时退出应用
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
});